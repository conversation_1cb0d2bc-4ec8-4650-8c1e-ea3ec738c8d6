package com.paic.ncbs.claim.controller.who.investigate;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.controller.doc.DocAppFileUploadController;
import com.paic.ncbs.claim.controller.doc.DocumentController;
import com.paic.ncbs.claim.controller.doc.IOBSFileUploadController;
import com.paic.ncbs.claim.controller.report.QueryReportController;
import com.paic.ncbs.claim.controller.who.WholeCaseController;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateQueryDTO;
import com.paic.ncbs.claim.model.dto.report.GetReportBaseInfoSearchVO;
import com.paic.ncbs.claim.model.dto.report.ReportBaseInfoResData;
import com.paic.ncbs.claim.model.vo.endcase.WholeCaseVO;
import com.paic.ncbs.claim.model.vo.endcase.WholeCasePageResult;
import com.paic.ncbs.claim.model.vo.fileupolad.FileInfoVO;
import com.paic.ncbs.claim.model.vo.investigate.InvestigateAuditVO;
import com.paic.ncbs.claim.model.vo.investigate.InvestigateProcessVO;
import com.paic.ncbs.claim.model.vo.investigate.InvestigateTaskVO;
import com.paic.ncbs.claim.model.vo.investigate.InvestigateVO;
import com.paic.ncbs.claim.model.vo.report.ReportCustomerInfoVO;
import com.paic.ncbs.claim.model.vo.taskdeal.ClaimInfoToESVO;
import com.paic.ncbs.claim.model.vo.taskdeal.WorkBenchTaskVO;
import com.paic.ncbs.claim.model.vo.endcase.TrackInfoVO;
import com.paic.ncbs.claim.service.endcase.WholeCaseService;
import com.paic.ncbs.claim.common.page.Pager;
import com.paic.ncbs.claim.service.investigate.InvestigateService;
import com.paic.ncbs.claim.service.investigate.InvestigateTaskAuditPublicService;
import com.paic.ncbs.claim.service.investigate.InvestigateTaskService;
import com.paic.ncbs.claim.service.other.impl.TaskListService;
import com.paic.ncbs.claim.service.report.ReportCustomerInfoService;
import com.paic.ncbs.file.service.FileCommonService;
import com.paic.ncbs.um.config.UmProperties;
import com.paic.ncbs.um.model.dto.QueryUserViewRequestDTO;
import com.paic.ncbs.um.model.dto.QueryUserViewResponseDTO;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import com.paic.ncbs.um.sao.mesh.NcbsUmRequest;
import com.paic.ncbs.um.util.Sha1SignUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Api(tags = "外部系统调查")
@RestController
@RequestMapping(value = "/public/app/investigateTaskAuditAction")
public class InvestigateTaskAuditPublicController {

    @Autowired
    private InvestigateTaskAuditPublicService investigateTaskAuditPublicService;

    @Autowired
    private QueryReportController queryReportController;

    @Autowired
    private DocAppFileUploadController docAppFileUploadController;

    @Autowired
    private IOBSFileUploadController iobsFileUploadController;

    @Autowired
    private DocumentController documentController;

    @Autowired
    private WholeCaseController wholeCaseController;

    @Autowired
    private TaskListService taskListService;

    @Autowired
    private ReportCustomerInfoService reportCustomerInfoService;

    @Autowired
    private InvestigateService investigateService;

    @Autowired
    private InvestigateTaskService investigateTaskService;

    @Autowired
    private FileCommonService fileCommonService;

    @Autowired
    private OcasMapper ocasMapper;

    @Autowired
    private WholeCaseService wholeCaseService;
    @Autowired
    private NcbsUmRequest ncbsUmRequest;
    @Autowired
    private UmProperties umProperties;

    /**
     * 统一接口中转
     */
    @ApiOperation(value = "统一接口中转")
    @PostMapping(value = "/transfer")
    public ResponseResult<?> transfer(@RequestBody InvestigateQueryDTO queryDTO) {
        if (queryDTO.getRequestData() == null) {
            throw new GlobalBusinessException("请求数据不能为空");
        }
        
        String interfaceName = queryDTO.getRequestData().getInterfaceName();
        if (interfaceName == null || interfaceName.trim().isEmpty()) {
            throw new GlobalBusinessException("接口名称不能为空");
        }

        try {
            java.lang.reflect.Method method = this.getClass().getMethod(interfaceName, InvestigateQueryDTO.class);
            return (ResponseResult<?>) method.invoke(this, queryDTO);
        } catch (NoSuchMethodException e) {
            throw new GlobalBusinessException("不支持的接口名称: " + interfaceName);
        } catch (GlobalBusinessException e) {
            throw e;
        } catch (Exception e) {
            LogUtil.error("#外部系统调查#接口中转异常#interfaceName={}", interfaceName, e);
            throw new GlobalBusinessException("接口调用失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "查询调查任务列表")
    @PostMapping(value = "/getInvestigateTaskList")
    public ResponseResult<Map<String, List<WorkBenchTaskVO>>> getInvestigateTaskList(@RequestBody InvestigateQueryDTO queryDTO) throws Exception {
        InvestigateQueryDTO.RequestData requestData = queryDTO.getRequestData();
        // 获取工作台任务列表
        com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskQueryDTO taskQueryDTO = new com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskQueryDTO();
        taskQueryDTO.setIsIncludeSubordinates("Y");
        taskQueryDTO.setIsMyCase("N");
        taskQueryDTO.setIsQuickPay("N");
        List<WorkBenchTaskVO> workBenchTaskList = investigateTaskAuditPublicService.getInvestigateTaskList(taskQueryDTO);

        // 按任务类型分组
        Map<String, List<WorkBenchTaskVO>> filteredTaskList = workBenchTaskList.stream()
                .filter(vo -> BpmConstants.OC_INVESTIGATE_APPROVAL.equals(vo.getTaskDefinitionBpmKey()))
                .collect(Collectors.groupingBy(WorkBenchTaskVO::getTaskDefinitionBpmKey));

        return ResponseResult.success(filteredTaskList);
    }

    @ApiOperation(value = "提交调查审核任务")
    @PostMapping(value = "/finishTaskAudit")
    public ResponseResult<Object> finishTaskAudit(@RequestBody InvestigateQueryDTO queryDTO) {
        InvestigateQueryDTO.RequestData requestData = queryDTO.getRequestData();
        investigateTaskAuditPublicService.finishTaskAudit(requestData.getTaskAuditInfo());
        return ResponseResult.success();
    }

    /**
     * 查询历史案件列表
     */
    @ApiOperation(value = "查询历史案件列表")
    @PostMapping(value = "/getHistoryCaseListNew")
    public ResponseResult<Map<String, Object>> getHistoryCaseListNew(@RequestBody InvestigateQueryDTO queryDTO) {
        InvestigateQueryDTO.RequestData requestData = queryDTO.getRequestData();

        LogUtil.audit("#外部系统调查#历史案件列表查询#reportNo={}, caseTimes={}", requestData.getReportNo(), requestData.getCaseTimes());

        // 创建分页对象
        Pager pager = new Pager();
        pager.setPageIndex(requestData.getPageIndex());
        pager.setPageRows(requestData.getPageRows());

        List<ClaimInfoToESVO> claimInfoToESVOs = reportCustomerInfoService.getHistoryCaseListNew(requestData.getReportNo(), requestData.getCaseTimes(), pager);
        return ResponseResult.success(claimInfoToESVOs, pager);
    }

    /**
     * 查询所有单证文件
     */
    @ApiOperation("查询所有单证文件")
    @PostMapping(value = "/getDocumentList")
    public ResponseResult<List<FileInfoVO>> getDocumentList(@RequestBody InvestigateQueryDTO queryDTO) throws GlobalBusinessException {
        InvestigateQueryDTO.RequestData requestData = queryDTO.getRequestData();
        LogUtil.audit("#外部系统调查#查看单证#reportNo={}, caseTimes={}", requestData.getReportNo(), requestData.getCaseTimes());

        return docAppFileUploadController.getDocumentList(requestData.getFileUploadInfo());
    }

    /**
     * 查询单证文件地址
     */
    @ApiOperation("查询单证文件地址")
    @PostMapping(value = "/getIntranetIOBSDownloadUrl")
    public ResponseResult<Object> getIntranetIOBSDownloadUrl(@RequestBody InvestigateQueryDTO queryDTO) {
        InvestigateQueryDTO.RequestData requestData = queryDTO.getRequestData();
        LogUtil.audit("#外部系统调查#获取单证地址#fileId={}, fileName={}", requestData.getFileId(), requestData.getFileName());

        return iobsFileUploadController.getIntranetIOBSDownloadUrl(requestData.getFileId(), requestData.getFileName());
    }

    /**
     * 查询上传单证类型列表
     */
    @ApiOperation("查询上传单证类型列表")
    @PostMapping(value = "/getAllDocumentTypeList")
    public ResponseResult<Object> getAllDocumentTypeList(@RequestBody InvestigateQueryDTO queryDTO) throws GlobalBusinessException {
        InvestigateQueryDTO.RequestData requestData = queryDTO.getRequestData();
        LogUtil.audit("#外部系统调查#获取单证类型#");

        return documentController.getAllDocumentTypeList(requestData.getFileInfo());
    }

    /**
     * 查询调查任务详情
     */
    @ApiOperation("查询调查任务详情")
    @PostMapping(value = "/getIntegratedInvestigateInfo")
    public ResponseResult<Map<String, Object>> getIntegratedInvestigateInfo(@RequestBody InvestigateQueryDTO queryDTO) {
        InvestigateQueryDTO.RequestData requestData = queryDTO.getRequestData();
        LogUtil.audit("#外部系统调查#整合查询接口#reportNo={}, caseTimes={}, investigateId={}, investigateTaskId={}",
            requestData.getReportNo(), requestData.getCaseTimes(), requestData.getInvestigateId(), requestData.getInvestigateTaskId());

        Map<String, Object> result = new HashMap<>();

        try {
            // 1. 获取真实被保人列表：/report/getCustomerInfoByReportNo
            ReportCustomerInfoVO customerInfo = queryReportController.getCustomerInfoByReportNo(requestData.getReportNo()).getData();
            result.put("customerInfo", customerInfo);

            // 2. 报案信息：/who/app/wholeCaseAction/getReportBaseInfo
            GetReportBaseInfoSearchVO searchVO = new GetReportBaseInfoSearchVO();
            searchVO.setReportNo(requestData.getReportNo());
            searchVO.setCaseTimes(requestData.getCaseTimes());
            ReportBaseInfoResData reportBaseInfo = wholeCaseController.getReportBaseInfo(searchVO).getData();
            result.put("reportBaseInfo", reportBaseInfo);

            // 3. 查询案件的历史调查信息：/who/app/investigateAction/getHistoryInvestigateByReportNo
            List<InvestigateVO> historyInvestigateList = investigateService.getHistoryInvestigate(requestData.getReportNo(), requestData.getCaseTimes());
            result.put("historyInvestigateList", historyInvestigateList);

            // 4. 本次提调信息: /who/app/investigateAction/getInvestigateById
            InvestigateVO currentInvestigate = null;
            if (StringUtils.isNotEmpty(requestData.getInvestigateId())) {
                currentInvestigate = investigateService.getInvestigateById(requestData.getInvestigateId());
            }
            result.put("currentInvestigate", currentInvestigate);

            // 5. 分配信息查询: /who/app/investigateTaskAction/getInvestigateTaskLinkedByTaskId - 只需要 investigateAuditVO
            InvestigateAuditVO investigateAuditVO = null;
            if (StringUtils.isNotEmpty(requestData.getInvestigateTaskId())) {
                InvestigateTaskVO taskInfo = investigateTaskService.getInvestigateTaskLinkedByTaskId(requestData.getInvestigateTaskId());
                if (taskInfo != null) {
                    investigateAuditVO = taskInfo.getInvestigateAuditVO();
                }
            }
            result.put("investigateAuditVO", investigateAuditVO);

            // 6. 调查任务处理：/who/app/investigateTaskAction/getInvestigateTaskLinkedByTaskId - 只需要 investigateProcessVOs
            List<InvestigateProcessVO> investigateProcessVOs = null;
            if (StringUtils.isNotEmpty(requestData.getInvestigateTaskId())) {
                InvestigateTaskVO taskInfo = investigateTaskService.getInvestigateTaskLinkedByTaskId(requestData.getInvestigateTaskId());
                if (taskInfo != null) {
                    investigateProcessVOs = taskInfo.getInvestigateProcessVOs();
                }
            }
            result.put("investigateProcessVOs", investigateProcessVOs);

            LogUtil.audit("#外部系统调查#整合查询接口#成功获取所有信息");

        } catch (Exception e) {
            LogUtil.error("#外部系统调查#整合查询接口#异常", e);
            throw new GlobalBusinessException("获取调查信息失败：" + e.getMessage());
        }

        return ResponseResult.success(result);
    }

    /**
     * 查询电子保单地址
     */
    @ApiOperation("查询电子保单地址")
    @PostMapping(value = "/getPolicyElectricPdfUrl")
    public ResponseResult<String> getPolicyElectricPdfUrl(@RequestBody InvestigateQueryDTO queryDTO) {
        InvestigateQueryDTO.RequestData requestData = queryDTO.getRequestData();
        LogUtil.audit("#外部系统调查#电子保单下载#policyNo={}", requestData.getPolicyNo());

        try {
            String previewUrl = null;
            try {
                //查询电子保单预览URL
                String policyDocumentId = ocasMapper.getPolicyDocumentId(requestData.getPolicyNo());
                UserInfoDTO userDTO = WebServletContext.getUser(); // 外部系统用户
                previewUrl = fileCommonService.getPreviewUrl(policyDocumentId, userDTO.getUserName());
            } catch (Exception e) {
                LogUtil.error("保单号：{}获取预览失败，失败原因：{}", requestData.getPolicyNo(), e.getMessage());
            }

            return ResponseResult.success(previewUrl);

        } catch (Exception e) {
            LogUtil.error("#外部系统调查#电子保单下载#异常", e);
            throw new GlobalBusinessException("获取电子保单下载地址失败：" + e.getMessage());
        }
    }

    /**
     * 查询整案案件列表
     */
    @ApiOperation("查询整案案件列表")
    @PostMapping(value = "/getHistoryCaseList")
    public ResponseResult<Object> getHistoryCaseList(@RequestBody InvestigateQueryDTO queryDTO) {
        try {
            InvestigateQueryDTO.RequestData requestData = queryDTO.getRequestData();
            if (StringUtils.isEmptyStr(queryDTO.getRequestData().getReportNo())) {
                throw new GlobalBusinessException("报案号不能为空");
            }
            String systemCode = umProperties.getSystemCode();

            QueryUserViewRequestDTO queryUserViewRequestDTO = new QueryUserViewRequestDTO();
            queryUserViewRequestDTO.setAccount(requestData.getUserCode());
            queryUserViewRequestDTO.setSystemId(systemCode);
            queryUserViewRequestDTO.setGatewayName("solomon");
            String nonce = RandomStringUtils.randomAlphanumeric(32);
            String timestamp = String.valueOf(System.currentTimeMillis());
            String token = umProperties.getToken();
            String clientCode = umProperties.getClientCode();
            String signature = Sha1SignUtil.generateSign(token, timestamp, nonce).toLowerCase();
            QueryUserViewResponseDTO queryUserViewResponseDTO = ncbsUmRequest.queryUserView(queryUserViewRequestDTO, timestamp, nonce, signature, clientCode);

            WholeCaseVO wholeCaseVO = new WholeCaseVO();
            wholeCaseVO.setReportNo(requestData.getReportNo());
            wholeCaseVO.setCaseTimes(requestData.getCaseTimes());

            WholeCasePageResult caseListResult = wholeCaseService.getHistoryCaseList(wholeCaseVO);
            List<WholeCaseVO> caseList = caseListResult.getList();

             if (queryUserViewResponseDTO != null && queryUserViewResponseDTO.getResult() != null) {
                 List<QueryUserViewResponseDTO.Role> roles = queryUserViewResponseDTO.getResult().getRoles();
                 if (roles != null) {
                     boolean hasTPARole = roles.stream()
                             .anyMatch(role -> "1854".equals(role.getRoleId()) || "1853".equals(role.getRoleId()));

                     if (hasTPARole) {
                         // TPA角色只能查看自己的案件
                         caseList = caseList.stream()
                                 .filter(caseVO -> requestData.getUserCode().equals(caseVO.getAssigner()))
                                 .collect(Collectors.toList());
                     }
                 }
             }

            return ResponseResult.success(caseList);
        } catch (GlobalBusinessException e) {
            return ResponseResult.fail(e.getCode(), e.getMessage());
        }
    }

    /**
     * 查询整案案件详情
     */
    @ApiOperation("查询整案案件详情")
    @PostMapping(value = "/getCaseInfo")
    public ResponseResult<List<TrackInfoVO>> getCaseInfo(@RequestBody InvestigateQueryDTO queryDTO) {
        InvestigateQueryDTO.RequestData requestData = queryDTO.getRequestData();
        return ResponseResult.success(wholeCaseService.getTrackInfoList(requestData.getReportNo(), requestData.getCaseTimes()));
    }



}