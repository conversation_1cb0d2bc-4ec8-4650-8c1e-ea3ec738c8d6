<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.secondunderwriting.ClmsPolicySurrenderInfoMapper">

    <resultMap type="com.paic.ncbs.claim.dao.entity.clms.ClmsPolicySurrenderInfoEntity" id="ClmsPolicySurrenderInfoMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="reportNo" column="report_no" jdbcType="VARCHAR"/>
        <result property="caseTimes" column="case_times" jdbcType="INTEGER"/>
        <result property="policyNo" column="policy_no" jdbcType="VARCHAR"/>
        <result property="idSeconduwPolicyConclusion" column="id_seconduw_policy_conclusion" jdbcType="VARCHAR"/>
        <result property="surrenderType" column="surrender_type" jdbcType="VARCHAR"/>
        <result property="surrenderReason" column="surrender_reason" jdbcType="VARCHAR"/>
        <result property="effectiveDate" column="effective_date" jdbcType="TIMESTAMP"/>
        <result property="totalAgreePremium" column="total_agree_premium" jdbcType="VARCHAR"/>
        <result property="totalActualPremium" column="total_actual_premium" jdbcType="VARCHAR"/>
        <result property="unearnedPrem" column="unearned_prem" jdbcType="VARCHAR"/>
        <result property="refundType" column="refund_type" jdbcType="VARCHAR"/>
        <result property="syncPosStatus" column="sync_pos_status" jdbcType="VARCHAR"/>
        <result property="refundAmount" column="refund_amount" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDate" column="created_date" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedDate" column="updated_date" jdbcType="TIMESTAMP"/>
    </resultMap>
    <resultMap type="com.paic.ncbs.claim.model.vo.senconduw.ClmsPolicySurrenderInfoVO" id="surrenderInfoMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="reportNo" column="report_no" jdbcType="VARCHAR"/>
        <result property="caseTimes" column="case_times" jdbcType="INTEGER"/>
        <result property="policyNo" column="policy_no" jdbcType="VARCHAR"/>
        <result property="idSeconduwPolicyConclusion" column="id_seconduw_policy_conclusion" jdbcType="VARCHAR"/>
        <result property="surrenderType" column="surrender_type" jdbcType="VARCHAR"/>
        <result property="surrenderReason" column="surrender_reason" jdbcType="VARCHAR"/>
        <result property="effectiveDate" column="effective_date" jdbcType="TIMESTAMP"/>
        <result property="totalAgreePremium" column="total_agree_premium" />
        <result property="totalActualPremium" column="total_actual_premium" />
        <result property="unearnedPrem" column="unearned_prem" />
        <result property="refundType" column="refund_type" jdbcType="VARCHAR"/>
        <result property="syncPosStatus" column="sync_pos_status" jdbcType="VARCHAR"/>
        <result property="refundAmount" column="refund_amount" />
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDate" column="created_date" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedDate" column="updated_date" jdbcType="TIMESTAMP"/>
        <result property="policyNo" column="policy_no"/>
        <result property="productName" column="product_name"/>
        <result property="schemeName" column="scheme_name"/>
        <result property="insuranceBeginDate" column="insurance_begin_date"/>
        <result property="insuranceEndDate" column="insurance_end_date"/>
        <result property="insuredName" column="insured_name"/>
        <result property="selectFlag"  column="selectFlag"/>
        <result property="fileId" column="file_id"/>
        <result property="collegialFlag" column="collegial_flag"/>
        <result property="collegialFileId" column="collegial_file_id"/>
        <result property="endorseComment" column="ENDORSE_COMMENT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, report_no, case_times, policy_no, id_seconduw_policy_conclusion, surrender_type, surrender_reason, effective_date, total_agree_premium, total_actual_premium, unearned_prem, refund_type, sync_pos_status, refund_amount, created_by, created_date, updated_by, updated_date,file_id,collegial_file_id,ENDORSE_COMMENT
    </sql>

    <!--查询单个-->
    <select id="getSurrenderInfo" resultMap="ClmsPolicySurrenderInfoMap">
        select
        <include refid="Base_Column_List"/>
        from clms_policy_surrender_info
        where report_no = #{reportNo}
        <if test="caseTimes != null and caseTimes != ''">
            and case_times = #{caseTimes}
        </if>
    </select>
    <!-- 查询反显理赔解约保存信息-->
    <select id="getSurrenderList" resultMap="surrenderInfoMap">
        select a.policy_no ,a.product_name ,a.scheme_name ,a.insurance_begin_date,a.insurance_end_date ,a.insured_name ,a.id id_seconduw_policy_conclusion ,
        b.surrender_type,b.surrender_reason,b.effective_date ,b.unearned_prem,b.total_agree_premium,b.total_actual_premium ,b.refund_type,b.refund_amount,b.id,if(isnull(b.id),'0','1') selectFlag,
        b.file_id,b.collegial_flag,b.collegial_file_id,b.created_by,b.sync_pos_status,b.ENDORSE_COMMENT
        from clms_seconduw_policy_conclusion a
        left join clms_policy_surrender_info b
        on a.id =b.id_seconduw_policy_conclusion
        where  a.id_clms_second_underwriting =#{idClmsSecondUnderwriting}
        and not exists (select 1 from clms_policy_surrender_info c where a.REPORT_NO = c.REPORT_NO  and a.policy_no = c.policy_no and c.sync_pos_status = '1')
    </select>

    <!--查询单个-->
    <select id="getSurrenderCount" resultType="int">
        select
        count(1)
        from clms_policy_surrender_info
        where report_no = #{reportNo}
        <if test="caseTimes != null and caseTimes != ''">
            and case_times = #{caseTimes}
        </if>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into clms_policy_surrender_info(id, report_no, case_times, policy_no, id_seconduw_policy_conclusion,
                                               surrender_type, surrender_reason, effective_date, total_agree_premium,
                                               total_actual_premium, unearned_prem, refund_type, sync_pos_status,
                                               refund_amount, created_by, created_date, updated_by, updated_date,file_id,collegial_flag,collegial_file_id,endorse_comment)
        values (#{id}, #{reportNo}, #{caseTimes}, #{policyNo}, #{idSeconduwPolicyConclusion}, #{surrenderType},
                #{surrenderReason}, #{effectiveDate}, #{totalAgreePremium}, #{totalActualPremium}, #{unearnedPrem},
                #{refundType}, #{syncPosStatus}, #{refundAmount}, #{createdBy}, #{createdDate}, #{updatedBy},
                #{updatedDate},#{entity.fileId},#{entity.collegialFlag},#{entity.collegialFileId},#{entity.endorseComment})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into clms_policy_surrender_info(id, report_no, case_times, policy_no, id_seconduw_policy_conclusion,
        surrender_type, surrender_reason, effective_date, total_agree_premium, total_actual_premium, unearned_prem,
        refund_type, sync_pos_status, refund_amount, created_by, created_date, updated_by, updated_date,file_id,collegial_flag,collegial_file_id,ENDORSE_COMMENT)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id}, #{entity.reportNo}, #{entity.caseTimes}, #{entity.policyNo},
            #{entity.idSeconduwPolicyConclusion}, #{entity.surrenderType}, #{entity.surrenderReason},
            #{entity.effectiveDate}, #{entity.totalAgreePremium}, #{entity.totalActualPremium}, #{entity.unearnedPrem},
            #{entity.refundType}, #{entity.syncPosStatus}, #{entity.refundAmount}, #{entity.createdBy},
            #{entity.createdDate}, #{entity.updatedBy}, #{entity.updatedDate},#{entity.fileId},#{entity.collegialFlag},#{entity.collegialFileId},#{entity.endorseComment})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update clms_policy_surrender_info
        <set>
            <if test="reportNo != null and reportNo != ''">
                report_no = #{reportNo},
            </if>
            <if test="caseTimes != null">
                case_times = #{caseTimes},
            </if>
            <if test="policyNo != null and policyNo != ''">
                policy_no = #{policyNo},
            </if>
            <if test="idSeconduwPolicyConclusion != null and idSeconduwPolicyConclusion != ''">
                id_seconduw_policy_conclusion = #{idSeconduwPolicyConclusion},
            </if>
            <if test="surrenderType != null and surrenderType != ''">
                surrender_type = #{surrenderType},
            </if>
            <if test="surrenderReason != null and surrenderReason != ''">
                surrender_reason = #{surrenderReason},
            </if>
            <if test="effectiveDate != null">
                effective_date = #{effectiveDate},
            </if>
            <if test="totalAgreePremium != null">
                total_agree_premium = #{totalAgreePremium},
            </if>
            <if test="totalActualPremium != null">
                total_actual_premium = #{totalActualPremium},
            </if>
            <if test="unearnedPrem != null">
                unearned_prem = #{unearnedPrem},
            </if>
            <if test="refundType != null and refundType != ''">
                refund_type = #{refundType},
            </if>
            <if test="syncPosStatus != null and syncPosStatus != ''">
                sync_pos_status = #{syncPosStatus},
            </if>
            <if test="refundAmount != null">
                refund_amount = #{refundAmount},
            </if>
            <if test="createdBy != null and createdBy != ''">
                created_by = #{createdBy},
            </if>
            <if test="createdDate != null">
                created_date = #{createdDate},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedDate != null">
                updated_date = #{updatedDate},
            </if>
            <if test="endorseComment != null and endorseComment != ''">
                endorse_comment = #{endorseComment},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from clms_policy_surrender_info
        where id = #{id}
    </delete>

    <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
        insert into clms_policy_surrender_info(id, report_no, case_times, policy_no, id_seconduw_policy_conclusion,
                                               surrender_type, surrender_reason, effective_date, total_agree_premium,
                                               total_actual_premium, unearned_prem, refund_type, sync_pos_status,
                                               refund_amount, created_by, created_date, updated_by, updated_date,endorse_comment)
        select replace(UUID(), '-', ''),
               report_no,
               #{reopenCaseTimes},
               policy_no,
               id_seconduw_policy_conclusion,
               surrender_type,
               surrender_reason,
               effective_date,
               total_agree_premium,
               total_actual_premium,
               unearned_prem,
               refund_type,
               sync_pos_status,
               refund_amount,
               #{userId},
               NOW(),
               #{userId},
               NOW(),
               endorse_comment
        from clms_policy_surrender_info
        where REPORT_NO = #{reportNo}
          AND CASE_TIMES = #{caseTimes}
    </insert>
   <delete id="deleteByReportNoAndCaseTimes">
       delete from clms_policy_surrender_info cpsi
       where report_no =#{reportNo}
       and case_times =#{caseTimes}
   </delete>
    <!-- 保单理赔在途校验：查询非已结案(WHOLE_CASE_STATUS=0-已结案)，非当前报案号 的数据   -->
    <select id="getClaimingInfo" parameterType="java.lang.String" resultType="java.lang.String">
        select cpcc.REPORT_NO from clms_policy_claim_case cpcc,clm_whole_case_base cti
        where
        cpcc.REPORT_NO = cti.REPORT_NO
        and  cpcc.POLICY_NO =#{policyNo}
        and cti.WHOLE_CASE_STATUS !='0'
        and cti.REPORT_NO !=#{reportNo}
        limit 1
    </select>

    <select id="getPolicyReportAccdentDate" parameterType="java.lang.String" resultType="java.util.Date">
        select  crc.ACCIDENT_DATE   from clms_policy_claim_case cpcc,clm_report_accident crc
        where cpcc.REPORT_NO = crc.REPORT_NO
        and POLICY_NO =#{policyNo}
        order by crc.ACCIDENT_DATE  desc limit 1
    </select>

    <select id="getSurrenderListByReportNo"  resultMap="surrenderInfoMap">
        select <include refid="Base_Column_List"></include>
        from clms_policy_surrender_info where
        report_no=#{reportNo}
        and case_times=#{caseTimes}
        and sync_pos_status='0'
    </select>

    <select id="getSurrenderSuccessCount" resultMap="surrenderInfoMap">
        select
        <include refid="Base_Column_List"></include>
        from clms_policy_surrender_info
        where policy_no = #{policyNo}
        and sync_pos_status = '1'
    </select>

</mapper>

