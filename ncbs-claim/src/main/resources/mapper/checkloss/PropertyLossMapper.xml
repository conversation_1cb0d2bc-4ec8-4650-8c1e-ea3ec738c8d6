<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.checkloss.PropertyLossMapper">
    <resultMap type="com.paic.ncbs.claim.model.dto.checkloss.PropertyLossDTO" id="propertyLossListResult">
        <result column="REPORT_NO" property="reportNo"/>
        <result column="CASE_TIMES" property="caseTimes"/>
        <result column="ID_AHCS_CHANNEL_PROCESS" property="idAhcsChannelProcess"/>
        <result column="ACCIDENT_OVERSEAS" property="accidentOverseas"/>
        <result column="ACCIDENT_PROVINCE_CODE" property="provinceCode"/>
        <result column="ACCIDENT_CONTINENT_CODE" property="accidentContinentCode"/>
        <result column="ACCIDENT_CITY_CODE" property="accidentCityCode"/>
        <result column="ACCIDENT_COUNTY_CODE" property="accidentCountyCode"/>
        <result column="ACCIDENT_PLACE" property="accidentPlace"/>
        <result column="ACCIDENT_TYPE" property="accidentType"/>
        <result column="LOSS_AMOUNT" property="lossAmount"/>
        <result column="ALTER_REASON" property="alterReason"/>
        <result column="THIRD_PROPERTY_LOSS_CONTENT" property="thirdPropertyLossContent"/>
        <result column="THIRD_PROPERTY_LOSS_AMOUNT" property="thirdPropertyLossAmount"/>
        <result column="TASK_CODE" property="taskCode"/>
        <result column="STATUS" property="status"/>
        <result column="ARCHIVE_TIME" property="archiveTime"/>
        <result column="ACCIDENT_DATE" property="accidentDate"/>
        <result column="ACCIDENT_CAUSE_CODE" property="accidentCauseCode"/>
        <result column="OTHER_ACCIDENT_CAUSE" property="otherAccidentCause"/>
    </resultMap>

    <insert id="addPropertyLoss">
        INSERT INTO CLMS_PROPERTY_LOSS
        (CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        REPORT_NO,
        CASE_TIMES,
        ID_AHCS_CHANNEL_PROCESS,
        ACCIDENT_OVERSEAS,
        ACCIDENT_PROVINCE_CODE,
        ACCIDENT_CONTINENT_CODE,
        ACCIDENT_CITY_CODE,
        ACCIDENT_COUNTY_CODE,
        ACCIDENT_PLACE,
        ACCIDENT_TYPE,
        LOSS_AMOUNT,
        ALTER_REASON,
        THIRD_PROPERTY_LOSS_CONTENT,
        THIRD_PROPERTY_LOSS_AMOUNT,
        TASK_CODE,
        STATUS,
        ACCIDENT_DATE,
        ACCIDENT_CAUSE_CODE,
        OTHER_ACCIDENT_CAUSE,
        ID_AHCS_PROPERTY_LOSS,
        IS_EFFECTIVE,
        archive_time
        )
        <foreach collection="propertyLossList" index="index" item="item" open="(" close=")" separator="union all">
            SELECT
            #{item.createdBy},
            SYSDATE(),
            #{item.updatedBy},
            SYSDATE(),
            #{item.reportNo},
            #{item.caseTimes},
            #{item.idAhcsChannelProcess},
            #{item.accidentOverseas},
            #{item.provinceCode},
            #{item.accidentContinentCode},
            #{item.accidentCityCode},
            #{item.accidentCountyCode},
            #{item.accidentPlace},
            #{item.accidentType},
            #{item.lossAmount},
            #{item.alterReason},
            #{item.thirdPropertyLossContent},
            #{item.thirdPropertyLossAmount},
            #{item.taskCode},
            #{item.status} ,
            #{item.accidentDate},
            #{item.accidentCauseCode},
            #{item.otherAccidentCause},
            left(hex(uuid()),32),
            'Y',
            <if test="item.archiveTime != null ">
                #{item.archiveTime}
            </if>
            <if test="item.archiveTime == null ">
                SYSDATE()
            </if>
            FROM DUAL
        </foreach>
    </insert>

    <delete id="removePropertyLoss">
        DELETE CLMS_PROPERTY_LOSS WHERE REPORT_NO = #{reportNo} AND CASE_TIMES = #{caseTimes}
        <if test="taskCode != null and taskCode != '' ">
            AND TASK_CODE = #{taskCode}
        </if>
        <if test="channelProcessId != null and channelProcessId != '' ">
            AND ID_AHCS_CHANNEL_PROCESS = #{channelProcessId}
        </if>
    </delete>

    <update id="updateEffective" parameterType="com.paic.ncbs.claim.model.dto.checkloss.PropertyLossDTO">
        UPDATE
        CLMS_PROPERTY_LOSS
        SET
        UPDATED_BY = #{updatedBy},
        UPDATED_DATE = SYSDATE(),
        IS_EFFECTIVE = 'N'
        WHERE
        REPORT_NO = #{reportNo}
        AND CASE_TIMES = #{caseTimes}
        <if test="taskCode != null and taskCode != '' ">
            AND TASK_CODE = #{taskCode}
        </if>
        <if test=" idAhcsChannelProcess != null and idAhcsChannelProcess !='' ">
            AND ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess}
        </if>
        AND IS_EFFECTIVE = 'Y'
    </update>

    <select id="getPropertyLoss" resultType="com.paic.ncbs.claim.model.dto.checkloss.PropertyLossDTO">
        SELECT REPORT_NO reportNo,
        CASE_TIMES caseTimes,
        ID_AHCS_CHANNEL_PROCESS idAhcsChannelProcess,
        ACCIDENT_OVERSEAS accidentOverseas,
        ACCIDENT_PROVINCE_CODE provinceCode,
        ACCIDENT_CONTINENT_CODE accidentContinentCode,
        ACCIDENT_CITY_CODE accidentCityCode,
        ACCIDENT_COUNTY_CODE accidentCountyCode,
        ACCIDENT_PLACE accidentPlace,
        ACCIDENT_TYPE accidentType,
        LOSS_AMOUNT lossAmount,
        ALTER_REASON alterReason,
        THIRD_PROPERTY_LOSS_CONTENT thirdPropertyLossContent,
        THIRD_PROPERTY_LOSS_AMOUNT thirdPropertyLossAmount,
        TASK_CODE taskCode,
        STATUS status,
        ACCIDENT_DATE accidentDate,
        ACCIDENT_CAUSE_CODE accidentCauseCode,
        OTHER_ACCIDENT_CAUSE otherAccidentCause
        FROM CLMS_PROPERTY_LOSS
        WHERE REPORT_NO = #{reportNo} AND CASE_TIMES = #{caseTimes}
        AND IS_EFFECTIVE = 'Y'
        <if test="status != null and status != '' ">
            AND STATUS = #{status}
        </if>
        <if test="taskCode != null and taskCode != '' ">
            AND TASK_CODE = #{taskCode}
        </if>
        <if test="channelProcessId != null and channelProcessId != '' ">
            AND ID_AHCS_CHANNEL_PROCESS = #{channelProcessId}
        </if>
    </select>


    <!-- 根据通道号、环节号获取 财产损失信息-->
    <select id="getPropertyLossList" parameterType="string" resultMap="propertyLossListResult">
        select REPORT_NO,
        ACCIDENT_OVERSEAS,
        ACCIDENT_PROVINCE_CODE,
        ACCIDENT_CONTINENT_CODE,
        ACCIDENT_CITY_CODE,
        ACCIDENT_COUNTY_CODE,
        ACCIDENT_PLACE,
        ACCIDENT_TYPE,
        LOSS_AMOUNT,
        ALTER_REASON,
        THIRD_PROPERTY_LOSS_CONTENT,
        THIRD_PROPERTY_LOSS_AMOUNT,
        TASK_CODE,
        STATUS,
        ARCHIVE_TIME,
        ACCIDENT_DATE,
        ACCIDENT_CAUSE_CODE,
        OTHER_ACCIDENT_CAUSE
        from CLMS_PROPERTY_LOSS pl
        where pl.ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess}
        and pl.STATUS = '1'
        and pl.TASK_CODE = #{taskCode}
        AND pl.IS_EFFECTIVE = 'Y'
    </select>

    <!-- 新增多条  财产损失信息 -->
    <insert id="addPropertyLossList">
        insert into CLMS_PROPERTY_LOSS
        (CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        REPORT_NO,
        CASE_TIMES,
        ID_AHCS_CHANNEL_PROCESS,
        ACCIDENT_OVERSEAS,
        ACCIDENT_PROVINCE_CODE,
        ACCIDENT_CONTINENT_CODE,
        ACCIDENT_CITY_CODE,
        ACCIDENT_COUNTY_CODE,
        ACCIDENT_PLACE,
        ACCIDENT_TYPE,
        LOSS_AMOUNT,
        ALTER_REASON,
        THIRD_PROPERTY_LOSS_CONTENT,
        THIRD_PROPERTY_LOSS_AMOUNT,
        TASK_CODE,
        STATUS,
        ACCIDENT_DATE,
        ACCIDENT_CAUSE_CODE,
        OTHER_ACCIDENT_CAUSE,
        ARCHIVE_TIME
        )
        <foreach collection="propertyLossList" index="index" item="item" open="(" close=")" separator="union all">
            select #{userId},
            SYSDATE(),
            #{userId},
            SYSDATE(),
            #{item.reportNo},
            #{caseTimes},
            #{channelProcessId},
            #{item.accidentOverseas},
            #{item.provinceCode},
            #{item.accidentContinentCode},
            #{item.accidentCityCode},
            #{item.accidentCountyCode},
            #{item.accidentPlace},
            #{item.accidentType},
            #{item.lossAmount},
            #{item.alterReason},
            #{item.thirdPropertyLossContent},
            #{item.thirdPropertyLossAmount},
            #{item.taskCode},
            #{item.status} ,
            #{item.accidentDate},
            #{item.accidentCauseCode},
            #{item.otherAccidentCause},
            <if test="item.archiveTime != null ">
                #{item.archiveTime}
            </if>
            <if test="item.archiveTime == null ">
                SYSDATE()
            </if>
            from DUAL
        </foreach>
    </insert>

    <update id="updatePropertyLoss" parameterType="com.paic.ncbs.claim.model.dto.checkloss.PropertyLossDTO">
        update CLMS_PROPERTY_LOSS
        <trim prefix="set" suffixOverrides=",">
            <if test="updatedBy != null">UPDATED_BY=#{updatedBy},</if>
            UPDATED_DATE=SYSDATE(),
            <if test="accidentType != null">ACCIDENT_TYPE=#{accidentType},</if>
            <if test="lossAmount != null">LOSS_AMOUNT=#{lossAmount},</if>
            <if test="alterReason != null">ALTER_REASON=#{alterReason},</if>
        </trim>
        WHERE REPORT_NO = #{reportNo}
        AND CASE_TIMES = #{caseTimes}
        AND TASK_CODE = #{taskCode}
        AND IS_EFFECTIVE = 'Y'
    </update>


    <insert id="insertPropertyLoss" parameterType="com.paic.ncbs.claim.model.dto.checkloss.PropertyLossDTO">
        insert into CLMS_PROPERTY_LOSS (
        CREATED_BY, CREATED_DATE,
        UPDATED_BY, UPDATED_DATE, REPORT_NO,
        CASE_TIMES, ID_AHCS_CHANNEL_PROCESS,
        ACCIDENT_TYPE, LOSS_AMOUNT, ALTER_REASON,
        TASK_CODE, STATUS
        )
        values (#{createdBy}, SYSDATE(),
        #{updatedBy}, SYSDATE(), #{reportNo},
        #{caseTimes}, #{idAhcsChannelProcess},
        #{accidentType}, #{lossAmount}, #{alterReason},
        #{taskCode}, #{status}
        )
    </insert>

    <select id="getPropertyLossByReportNo" resultType="com.paic.ncbs.claim.model.dto.checkloss.PropertyLossDTO">
        SELECT REPORT_NO reportNo,
        CASE_TIMES caseTimes,
        ID_AHCS_CHANNEL_PROCESS idAhcsChannelProcess,
        ACCIDENT_OVERSEAS accidentOverseas,
        ACCIDENT_PROVINCE_CODE provinceCode,
        ACCIDENT_CONTINENT_CODE accidentContinentCode,
        ACCIDENT_CITY_CODE accidentCityCode,
        ACCIDENT_COUNTY_CODE accidentCountyCode,
        ACCIDENT_PLACE accidentPlace,
        ACCIDENT_TYPE accidentType,
        LOSS_AMOUNT lossAmount,
        ALTER_REASON alterReason,
        THIRD_PROPERTY_LOSS_CONTENT thirdPropertyLossContent,
        THIRD_PROPERTY_LOSS_AMOUNT thirdPropertyLossAmount,
        TASK_CODE taskCode,
        STATUS status,
        ACCIDENT_DATE accidentDate,
        ACCIDENT_CAUSE_CODE accidentCauseCode,
        OTHER_ACCIDENT_CAUSE otherAccidentCause
        FROM CLMS_PROPERTY_LOSS
        WHERE REPORT_NO = #{reportNo} AND CASE_TIMES = #{caseTimes}
        AND IS_EFFECTIVE = 'Y'
        <if test="status != null and status != '' ">
            AND STATUS = #{status}
        </if>
        <if test="taskCode != null and taskCode != '' ">
            AND TASK_CODE = #{taskCode}
        </if>
        and TASK_CODE =
        (select * from
        (select t1.TASK_CODE from CLMS_PROPERTY_LOSS t1 where
        t1.REPORT_NO=#{reportNo} and t1.CASE_TIMES=#{caseTimes}
        AND t1.IS_EFFECTIVE = 'Y'
        <if test="taskCode != null and taskCode != '' ">
            and t1.TASK_CODE = #{taskCode}
        </if>
        <if test="status != null and status != '' ">
            AND t1.STATUS = #{status}
        </if>
        order by t1.created_date desc)
        as temp limit 1
        )
    </select>
</mapper>