<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.investigate.InvestigateTaskAuditPublicMapper">

    <select id="getInvestigateTaskList" resultType="com.paic.ncbs.claim.model.vo.taskdeal.WorkBenchTaskVO">
        SELECT
            t.report_no AS "reportNo",          -- 报案号
            dd.DEPARTMENT_ABBR_NAME AS "policyDepartmentName",  -- 机构
            a.name AS "insuredName",             -- 被保险人
            cpi.PRODUCT_NAME AS "productName",   -- 产品名称
            -- 整案时长：原逻辑（重启类任务用更新时间，其他用报案时间）
            CASE
                WHEN t.TASK_DEFINITION_BPM_KEY IN ('OC_RESTART_CASE_APPROVAL', 'OC_RESTART_CASE_MODIFY') THEN TIMESTAMPDIFF(hour, crc.UPDATED_DATE, NOW())
                ELSE TIMESTAMPDIFF(hour, b.REPORT_DATE, NOW())
            END AS "caseLength",                 -- 整案时长
            DATEDIFF(NOW(), t.CREATED_DATE) AS "currentLength",  -- 当前时长
            t.ASSIGNEE_TIME AS "assigneeTime",   -- 派工时间
            t.APPLYER_NAME AS "submitName",      -- 提交人
            t.TASK_ID AS "taskId"                -- 主键ID
        FROM
            clms_task_info t
        JOIN clms_investigate_task cit ON t.task_id = cit.id_ahcs_investigate-- 关键关联：用 Investigate_Task 的 ID 关联 Task_Info 的 TASK_ID
        JOIN clms_report_customer a ON a.report_no = t.report_no       -- 关联报案客户（被保险人）
        JOIN clm_report_info b ON b.report_no = t.report_no           -- 关联报案信息（报案时间）
        JOIN clms_policy_info cpi ON cpi.REPORT_NO = t.REPORT_NO     -- 关联保单信息（产品名称）
        LEFT JOIN department_define dd ON dd.DEPARTMENT_CODE = t.DEPARTMENT_CODE  -- 关联部门表（机构）
        LEFT JOIN clm_restart_case_record crc ON crc.report_no = t.report_no AND crc.case_times = t.case_times  -- 重启记录（用于整案时长计算）
        WHERE
            t.status IN ('0', '3')  -- 任务状态（未完成/待处理）
            AND t.TASK_DEFINITION_BPM_KEY = 'OC_INVESTIGATE_APPROVAL'
            and cit.INVESTIGATE_DEPARTMENT = ''
    </select>

</mapper>