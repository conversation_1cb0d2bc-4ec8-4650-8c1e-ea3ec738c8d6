<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.blacklist.ClmsBlackListMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.blacklist.ClmsBlackList">
        <id column="id" property="id" />
        <result column="report_no" property="reportNo" />
        <result column="case_times" property="caseTimes" />
        <result column="party_type" property="partyType" />
        <result column="entity_type" property="entityType" />
        <result column="party_name" property="partyName" />
        <result column="id_type" property="idType" />
        <result column="id_num" property="idNum" />
        <result column="risk_type" property="riskType" />
        <result column="phone_num" property="phoneNum" />
        <result column="valid_flag" property="validFlag" />
        <result column="black_source" property="blackSource" />
        <result column="audit_status" property="auditStatus" />
        <result column="remark" property="remark" />
        <result column="created_by" property="createdBy" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="updated_by" property="updatedBy" />
        <result column="sys_utime" property="sysUtime" />
        <result column="related_report_no" property="relatedReportNo" />
    </resultMap>

    <!-- 动态条件查询 -->
    <select id="getBlackListByCondition" resultMap="BaseResultMap" parameterType="map">
        SELECT
        id,
        report_no,
        case_times,
        party_type,
        entity_type,
        party_name,
        id_type,
        id_num,
        risk_type,
        phone_num,
        valid_flag,
        black_source,
        audit_status,
        remark,
        related_report_no
        FROM clms_black_list
        WHERE audit_status IN ('2','4')
        <if test="partyType != null and partyType != ''">
            AND (
            <foreach item="item" index="index" collection="partyType.split(',')" separator=" OR ">
                party_type LIKE CONCAT('%,', #{item}, ',%') OR
                party_type LIKE CONCAT(#{item}, ',%') OR
                party_type LIKE CONCAT('%,', #{item}) OR
                party_type = #{item}
            </foreach>
            )
        </if>
        <if test="entityType != null and entityType != ''">
            AND (
            <foreach item="item" index="index" collection="entityType.split(',')" separator=" OR ">
                entity_type LIKE CONCAT('%,', #{item}, ',%') OR
                entity_type LIKE CONCAT(#{item}, ',%') OR
                entity_type LIKE CONCAT('%,', #{item}) OR
                entity_type = #{item}
            </foreach>
            )
        </if>
        <if test="partyName != null and partyName != ''">
            AND party_name = #{partyName}
        </if>
        <if test="idType != null and idType != ''">
            AND id_type = #{idType}
        </if>
        <if test="idNum != null and idNum != ''">
            AND id_num = #{idNum}
        </if>
        <if test="phoneNum != null and phoneNum != ''">
            AND phone_num = #{phoneNum}
        </if>
        <if test="validFlag != null and validFlag != ''">
            AND valid_flag = #{validFlag}
        </if>
        ORDER BY sys_utime DESC
    </select>
    <!-- 新增黑名单 -->
    <insert id="saveBlackList" parameterType="com.paic.ncbs.claim.dao.entity.blacklist.ClmsBlackList">
        INSERT INTO clms_black_list (
        id,
        report_no,
        case_times,
        party_type,
        entity_type,
        party_name,
        id_type,
        id_num,
        risk_type,
        phone_num,
        valid_flag,
        black_source,
        audit_status,
        remark,
        related_report_no,
        created_by,
        sys_ctime,
        updated_by,
        sys_utime
        ) VALUES (
        #{id},
        #{reportNo},
        #{caseTimes},
        #{partyType},
        #{entityType},
        #{partyName},
        #{idType},
        #{idNum},
        #{riskType},
        #{phoneNum},
        #{validFlag},
        #{blackSource},
        #{auditStatus},
        #{remark},
        #{relatedReportNo},
        #{createdBy},
        NOW(),
        #{updatedBy},
        NOW()
        )
    </insert>
    <!-- 修改黑名单记录 -->
    <update id="updateBlackList" parameterType="com.paic.ncbs.claim.dao.entity.blacklist.ClmsBlackList">
        UPDATE clms_black_list
        <set>
            <if test="reportNo != null and reportNo != ''">
                report_no = #{reportNo},
            </if>
            <if test="caseTimes != null">
                case_times = #{caseTimes},
            </if>
            <if test="partyType != null and partyType != ''">
                party_type = #{partyType},
            </if>
            <if test="riskType != null and riskType != ''">
                risk_type = #{riskType},
            </if>
            <if test="phoneNum != null and phoneNum != ''">
                phone_num = #{phoneNum},
            </if>
            <if test="validFlag != null and validFlag != ''">
                valid_flag = #{validFlag},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="auditStatus != null and auditStatus != ''">
                audit_status = #{auditStatus},
            </if>
            <if test="relatedReportNo != null and relatedReportNo != ''">
                related_report_no = #{relatedReportNo},
            </if>
            sys_utime = NOW(),
            updated_by = #{updatedBy}
        </set>
        WHERE id = #{id}
    </update>

    <select id="getBlackListById" resultMap="BaseResultMap" >
        select
        id,
        report_no,
        case_times,
        party_type,
        entity_type,
        party_name,
        id_type,
        id_num,
        risk_type,
        phone_num,
        valid_flag,
        black_source,
        audit_status,
        remark,
        related_report_no
        from clms_black_list
        where id = #{id}
    </select>

    <select id="countByNameAndCertNo" resultType="int">
        SELECT COUNT(1)
        FROM clms_black_list
        WHERE party_name = #{partyName}
        <choose>
            <when test="idNum != null and idNum != ''">
                AND id_num = #{idNum}
            </when>
            <otherwise>
                AND (id_num IS NULL OR id_num = '')
            </otherwise>
        </choose>
    </select>



</mapper>
